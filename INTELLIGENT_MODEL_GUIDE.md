# 智能模型选择MCP服务器使用指南

## 🎯 功能概述

本MCP服务器实现了智能模型选择功能，能够根据用户需求自动选择最适合的AI模型进行图片生成，无需手动配置模型参数。

## 🤖 内置模型（3个精选模型）

### 1. 苏-FLUX小红书极致真实V2
- **模型ID**: `yiwanji/FLUX_xiao_hong_shu_ji_zhi_zhen_shi_V2`
- **适用场景**: 真实风格图片、人物肖像、生活场景、小红书风格内容
- **特点**: 极致真实感、适合社交媒体内容
- **默认尺寸**: 1024x1024

### 2. 麦橘超然 MajicFlus
- **模型ID**: `MAILAND/majicflus_v1`
- **适用场景**: 高质量人像生成、亚洲女性肖像、角色设计
- **特点**: 专注人像质量、细节丰富
- **默认尺寸**: 768x1024

### 3. FLUX.1-Kontext-dev-lora-ArtAug
- **模型ID**: `DiffSynth-Studio/FLUX.1-Kontext-dev-lora-ArtAug`
- **适用场景**: 艺术创作、美学增强、创意设计
- **特点**: 自动美学增强、色彩优化、构图改善
- **默认尺寸**: 1024x1024
- **特殊功能**: 自动添加美学增强指令

## 🧠 智能选择机制

系统会根据以下因素自动选择最适合的模型：

1. **图片类型匹配** (30分)
   - 根据imageType参数匹配模型适用场景

2. **风格要求适配** (25分)
   - 分析style参数选择对应风格的模型

3. **提示词内容分析** (25-40分)
   - **动物/宠物相关**: 自动排除人像专用模型，优选小红书模型
   - 人像相关: 优选MajicFlus或小红书模型
   - 真实感要求: 优选小红书模型
   - 艺术创作: 优选ArtAug模型
   - 女性角色: 优选MajicFlus模型
   - 小红书/生活场景: 优选小红书模型

4. **质量要求评估** (20分)
   - ultra质量要求会优选高质量模型

## 🛠 可用工具

### 1. generate_image
基础图片生成功能
```json
{
  "prompt": "一个美丽的亚洲女性肖像",
  "width": 768,
  "height": 1024,
  "imageType": "portrait"
}
```

### 2. analyze_and_generate
智能分析网页内容并生成适配图片（推荐使用）
```json
{
  "webpageContent": "这是一个关于美食博客的网站，需要温馨的生活场景图片",
  "imageType": "hero",
  "context": "小红书风格，真实感强"
}
```

### 3. smart_size_inference
智能尺寸推断
```json
{
  "imageType": "avatar",
  "usageContext": "用户头像"
}
```

### 4. list_available_models
查看所有可用模型及其特点
```json
{}
```

## ⚙️ 配置要求

只需要在MCP配置中提供以下环境变量：

```json
{
  "mcpServers": {
    "zhihui-image": {
      "command": "node",
      "args": ["path/to/build/index.js"],
      "env": {
        "MODELSCOPE_API_KEY": "your-api-key",
        "OUTPUT_DIR": "path/to/output",
        "MAX_IMAGE_SIZE": "2048"
      }
    }
  }
}
```

## 🎨 使用示例

### 生成人像
```
用户: "生成一个优雅的亚洲女性肖像"
系统: 自动选择 MajicFlus 模型 (人像专用)
```

### 生成真实场景
```
用户: "生成一个小红书风格的咖啡店场景"
系统: 自动选择 小红书极致真实V2 模型
```

### 艺术创作
```
用户: "创作一幅抽象艺术作品"
系统: 自动选择 ArtAug 模型 (美学增强)
```

### 动物/宠物生成
```
用户: "生成一只可爱的小猫咪"
系统: 自动排除人像模型，选择小红书模型 ✅
```

### 通用需求
```
用户: "生成一个简单的logo设计"
系统: 自动选择最适合的模型（根据评分系统）
```

## 📊 选择结果查看

每次生成时，系统会在日志中显示：
- 选中的模型名称和ID
- 适合度评分
- 选择原因
- 最终使用的提示词（包含特殊指令）

## 🔧 高级功能

- **自动美学增强**: ArtAug模型会自动添加美学增强指令
- **尺寸优化**: 根据模型特点自动调整默认尺寸
- **提示词优化**: 根据模型特性优化提示词
- **质量分级**: 支持standard/high/ultra三个质量等级

## 🚀 优势

1. **零配置**: 无需手动选择模型，系统智能决策
2. **最优匹配**: 根据需求自动选择最适合的模型
3. **简化使用**: 用户只需关注创作内容，不需了解技术细节
4. **持续优化**: 选择算法可持续改进和扩展
